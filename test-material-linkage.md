# 课时类型与关联素材二级联动功能测试

## 功能说明
实现了课时类型选择器与关联素材选择器之间的联动功能，当用户选择不同的课时类型时，关联素材下拉框会自动显示对应类型的素材。

## 实现的功能

### 1. 课时类型选择
- 视频
- 音频  
- 文档

### 2. 素材联动逻辑
- **选择"视频"**: 关联素材下拉框显示所有视频类型素材
- **选择"音频"**: 关联素材下拉框显示所有音频类型素材
- **选择"文档"**: 关联素材下拉框显示所有文档类型素材

### 3. 技术实现要点

#### API调用
```typescript
// 导入素材库API
import { getVideoList } from '@/api/infra/materiallibrary/materialVideo'
import { getVoiceList } from '@/api/infra/materiallibrary/materialVoice'
import { getDocumentList } from '@/api/infra/materiallibrary/materialDocument'
```

#### 响应式数据
```typescript
// 素材相关数据
const materialLoading = ref(false)
const videoMaterials = ref<any[]>([])
const voiceMaterials = ref<any[]>([])
const documentMaterials = ref<any[]>([])

// 根据课时类型过滤的素材选项
const filteredMaterialOptions = computed(() => {
  const lessonType = currentLesson.lessonType
  let materials: any[] = []
  
  switch (lessonType) {
    case '视频':
      materials = videoMaterials.value
      break
    case '音频':
      materials = voiceMaterials.value
      break
    case '文档':
      materials = documentMaterials.value
      break
    default:
      materials = []
  }
  
  return materials.map((item) => ({
    label: `${item.name || item.documentName} (${item.description || '素材文件'})`,
    value: item.name || item.documentName,
    id: item.id,
    url: item.url || item.documentUrl
  }))
})
```

#### 联动处理方法
```typescript
const handleLessonTypeChange = async (lessonType: string) => {
  // 清空当前选择的素材
  currentLesson.materialName = ''
  currentLesson.materialFileUrl = ''
  
  // 根据课时类型加载对应的素材数据
  switch (lessonType) {
    case '视频':
      if (videoMaterials.value.length === 0) {
        await fetchVideoMaterials()
      }
      break
    case '音频':
      if (voiceMaterials.value.length === 0) {
        await fetchVoiceMaterials()
      }
      break
    case '文档':
      if (documentMaterials.value.length === 0) {
        await fetchDocumentMaterials()
      }
      break
  }
}
```

## 测试步骤

1. 打开课程管理页面
2. 进入"课程大纲"标签页
3. 添加或编辑课时
4. 在课时编辑弹窗中：
   - 选择课时类型为"视频"，观察关联素材下拉框是否显示视频素材
   - 选择课时类型为"音频"，观察关联素材下拉框是否显示音频素材
   - 选择课时类型为"文档"，观察关联素材下拉框是否显示文档素材

## 预期效果

- 课时类型与关联素材实现完美联动
- 切换课时类型时，关联素材选项自动更新
- 提供良好的用户体验，避免选择错误类型的素材
- 支持懒加载，只在需要时加载对应类型的素材数据

## 注意事项

- 素材数据在页面加载时预加载，提高响应速度
- 支持多种API返回格式的兼容处理
- 包含加载状态指示，提升用户体验
- 切换课时类型时会清空当前选择的素材，避免数据不一致
