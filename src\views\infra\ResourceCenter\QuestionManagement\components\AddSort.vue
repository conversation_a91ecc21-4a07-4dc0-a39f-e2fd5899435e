<template>
  <el-dialog v-model="visible" title="添加分类" width="500px" :close-on-click-modal="false" @close="onCancel">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="一级名称" prop="level1" required>
        <el-select v-model="form.level1" placeholder="请选择一级名称">
          <el-option v-for="item in level1Options" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="二级名称" prop="level2" required>
        <el-select v-model="form.level2" placeholder="请选择二级名称">
          <el-option v-for="item in level2Options" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="三级名称" prop="level3" required>
        <el-select v-model="form.level3" placeholder="请选择三级名称">
          <el-option v-for="item in level3Options" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="认定点名称" prop="certName" required>
        <el-input v-model="form.certName" placeholder="请输入认定点名称" />
      </el-form-item>
      <el-form-item label="业务模块" prop="biz" required>
        <el-select v-model="form.biz" placeholder="请选择业务模块">
          <el-option v-for="item in bizOptions" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
    </el-form>
    <div style="text-align:right;margin-top:24px">
      <el-button @click="onCancel">取消</el-button>
      <el-button type="primary" @click="onSubmit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const visible = defineModel<boolean>('visible', { default: false })
const formRef = ref()
const form = ref({
  level1: '',
  level2: '',
  level3: '',
  certName: '',
  biz: ''
})
const rules = {
  level1: [{ required: true, message: '请选择一级名称', trigger: 'change' }],
  level2: [{ required: true, message: '请选择二级名称', trigger: 'change' }],
  level3: [{ required: true, message: '请选择三级名称', trigger: 'change' }],
  certName: [{ required: true, message: '请输入认定点名称', trigger: 'blur' }],
  biz: [{ required: true, message: '请选择业务模块', trigger: 'change' }]
}
const level1Options = ['一级A', '一级B', '一级C']
const level2Options = ['二级A', '二级B', '二级C']
const level3Options = ['三级A', '三级B', '三级C']
const bizOptions = ['高校业务', '家政业务', '培训业务', '认证业务']
function onSubmit() {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      // TODO: 提交逻辑
      visible.value = false
    }
  })
}
function onCancel() {
  visible.value = false
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 18px;
}
</style>
