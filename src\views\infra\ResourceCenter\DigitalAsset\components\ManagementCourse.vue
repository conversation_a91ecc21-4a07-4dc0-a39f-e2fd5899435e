<template>
  <el-card>
    <div style="margin-bottom: 16px">
      <el-button type="default" @click="goBack" icon="el-icon-arrow-left">返回列表</el-button>
    </div>
    <el-tabs v-model="activeTab">
      <el-tab-pane label="课程设置" name="setting">
        <el-form
          :model="form"
          :rules="rules"
          ref="formRef"
          label-width="120px"
          class="course-form"
          v-loading="loading"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="课程名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入课程名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="课程状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择">
                  <el-option
                    v-for="item in statusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="课程分类" prop="category">
                <el-select v-model="form.category" placeholder="请选择">
                  <el-option
                    v-for="item in categoryOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="课程封面" prop="cover">
                <el-upload
                  class="upload-demo"
                  :show-file-list="true"
                  :auto-upload="false"
                  :on-change="handleCoverChange"
                >
                  <el-button>选择文件</el-button>
                  <template #tip>
                    <div class="el-upload__tip">{{
                      form.coverUrl ? '已选择文件' : '未选择任何文件'
                    }}</div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="关联讲师" prop="teacherName">
                <el-select
                  v-model="form.teacherId"
                  placeholder="请选择讲师"
                  @change="handleTeacherChange"
                >
                  <el-option
                    v-for="item in teacherOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收款商户" prop="merchant">
                <el-select
                  v-model="form.merchant"
                  placeholder="请选择收款商户"
                  @change="handleMerchantChange"
                >
                  <el-option
                    v-for="item in merchantOptions"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="业务模块" prop="businessModule">
                <el-select v-model="form.businessModule" placeholder="请选择业务模块">
                  <el-option
                    v-for="item in businessModuleOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上课地点" prop="location">
                <el-input v-model="form.location" placeholder="请输入上课地点" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排期" prop="schedule">
                <el-input v-model="form.schedule" placeholder="如：每周一、三、五 9:00-17:00" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="总名额" prop="totalSeats">
                <el-input v-model="form.totalSeats" placeholder="请输入总名额" type="number" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="已报名人数" prop="enrolledCount">
                <el-input
                  v-model="form.enrolledCount"
                  placeholder="请输入已报名人数"
                  type="number"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="课程详情介绍" prop="description">
                <el-input
                  v-model="form.description"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入课程详细介绍"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <div style="text-align: right; margin-top: 24px">
            <el-button @click="onCancel" :disabled="loading">取消</el-button>
            <el-button type="primary" @click="onSubmit" :loading="loading">保存</el-button>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="课程附件" name="attachment">
        <div class="attachment-section" v-loading="attachmentsLoading">
          <div class="attachment-title">已关联的附件</div>

          <!-- 附件列表 -->
          <el-table
            :data="attachmentList"
            style="width: 100%; margin-bottom: 16px"
            v-if="attachmentList.length > 0"
          >
            <el-table-column prop="attachmentName" label="文件名" />
            <el-table-column prop="attachmentType" label="类型" width="100">
              <template #default="scope">
                <el-tag
                  :type="
                    scope.row.attachmentType === '视频'
                      ? 'primary'
                      : scope.row.attachmentType === '文档'
                        ? 'success'
                        : 'warning'
                  "
                >
                  {{ scope.row.attachmentType }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="fileSize" label="文件大小" width="120">
              <template #default="scope">
                <span v-if="scope.row.fileSize">{{ formatFileSize(scope.row.fileSize) }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button type="primary" size="small" @click="downloadAttachment(scope.row)"
                  >下载</el-button
                >
                <el-button type="danger" size="small" @click="removeAttachment(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <el-empty description="暂无附件" />
          </div>

          <div class="attachment-title">从素材库添加</div>
          <el-row :gutter="16" align="middle" style="margin-bottom: 16px">
            <el-col :span="6">
              <el-select
                v-model="selectedMaterialType"
                placeholder="请选择素材类型"
                @change="handleMaterialTypeChange"
              >
                <el-option
                  v-for="item in materialTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-select
                v-model="selectedMaterial"
                placeholder="请选择素材"
                :loading="materialLoading"
                :disabled="!selectedMaterialType"
              >
                <el-option
                  v-for="item in filteredMaterialOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="addMaterial">添加附件</el-button>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import {
  DigitalAssetApi,
  type DigitalAssetCourse,
  type CourseAttachment,
  type AddAttachmentParams
} from '@/api/infra/digitalAsset'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { getAppList } from '@/api/pay/app'
import request from '@/config/axios'
// 导入素材库相关API
import { getVideoList } from '@/api/infra/materiallibrary/materialVideo'
import { getVoiceList } from '@/api/infra/materiallibrary/materialVoice'
import { getDocumentList } from '@/api/infra/materiallibrary/materialDocument'

// 定义 props 接口
interface Props {
  course?: any
}

const props = defineProps<Props>()
const emit = defineEmits(['goBack'])

const activeTab = ref('setting')
const formRef = ref()
const loading = ref(false)
const attachmentsLoading = ref(false)
const attachmentList = ref<CourseAttachment[]>([])
const selectedMaterial = ref('')

const form = reactive({
  id: undefined as number | undefined,
  name: '',
  teachType: '线下授课',
  coverUrl: '',
  category: '',
  status: '',
  teacherId: undefined as number | undefined,
  teacherName: '',
  businessModule: '',
  merchant: undefined as number | undefined,
  merchantName: '',
  description: '',
  // 线下授课专用字段
  location: '',
  schedule: '',
  totalSeats: undefined as number | undefined,
  enrolledCount: 0
})
const rules = {
  name: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
  status: [{ required: true, message: '请选择课程状态', trigger: 'change' }],
  category: [{ required: true, message: '请选择课程分类', trigger: 'change' }]
}
const statusOptions = [
  { label: '已上架', value: '已上架' },
  { label: '已下架', value: '已下架' },
  { label: '待发布', value: '待发布' }
]
const categoryOptions = [
  { label: '家政技能', value: '家政技能' },
  { label: '职业素养', value: '职业素养' },
  { label: '高校实践', value: '高校实践' },
  { label: '企业管理', value: '企业管理' }
]
const merchantOptions = ref<any[]>([])
const businessModuleOptions = ref<any[]>([])

// 素材相关数据
const materialLoading = ref(false)
const selectedMaterialType = ref('')
const videoMaterials = ref<any[]>([])
const voiceMaterials = ref<any[]>([])
const documentMaterials = ref<any[]>([])

// 素材类型选项
const materialTypeOptions = [
  { label: '视频', value: '视频' },
  { label: '音频', value: '音频' },
  { label: '文档', value: '文档' }
]

// 根据素材类型过滤的素材选项
const filteredMaterialOptions = computed(() => {
  const materialType = selectedMaterialType.value
  let materials: any[] = []

  switch (materialType) {
    case '视频':
      materials = videoMaterials.value
      break
    case '音频':
      materials = voiceMaterials.value
      break
    case '文档':
      materials = documentMaterials.value
      break
    default:
      materials = []
  }

  return materials.map((item) => ({
    label: `${item.name || item.documentName} (${item.description || '素材文件'})`,
    value: item.name || item.documentName,
    id: item.id,
    url: item.url || item.documentUrl,
    type: materialType,
    size: item.fileSize || 0
  }))
})

const teacherOptions = ref<any[]>([])

function handleCoverChange(file: any) {
  // 这里可以处理文件上传，获取文件URL后赋值给form.coverUrl
  console.log('文件变更:', file)
}

function handleTeacherChange(teacherId: number) {
  // 根据选择的讲师ID设置讲师信息
  const teacher = teacherOptions.value.find((item) => item.id === teacherId)
  if (teacher) {
    form.teacherId = teacher.id
    form.teacherName = teacher.name
  }
}

function handleMerchantChange(merchantId: number) {
  // 根据选择的商户ID设置商户名称
  const merchant = merchantOptions.value.find((item) => item.id === merchantId)
  if (merchant) {
    form.merchant = merchant.id
    form.merchantName = merchant.name
  }
}

// ==================== 素材获取方法 ====================

// 获取视频素材列表
const fetchVideoMaterials = async () => {
  try {
    materialLoading.value = true
    const params = {
      pageNo: 1,
      pageSize: 100 // 获取前100条数据
    }
    const res = await getVideoList(params)

    // 兼容多种返回格式
    if (Array.isArray(res)) {
      videoMaterials.value = res
    } else if (res && Array.isArray(res.data)) {
      videoMaterials.value = res.data
    } else if (res && res.data && Array.isArray(res.data.list)) {
      videoMaterials.value = res.data.list
    } else if (res && Array.isArray(res.list)) {
      videoMaterials.value = res.list
    } else {
      videoMaterials.value = []
    }
  } catch (error) {
    console.error('获取视频素材失败:', error)
    videoMaterials.value = []
  } finally {
    materialLoading.value = false
  }
}

// 获取音频素材列表
const fetchVoiceMaterials = async () => {
  try {
    materialLoading.value = true
    const params = {
      pageNo: 1,
      pageSize: 100
    }
    const res = await getVoiceList(params)

    // 兼容多种返回格式
    if (Array.isArray(res)) {
      voiceMaterials.value = res
    } else if (res && Array.isArray(res.data)) {
      voiceMaterials.value = res.data
    } else if (res && res.data && Array.isArray(res.data.list)) {
      voiceMaterials.value = res.data.list
    } else if (res && Array.isArray(res.list)) {
      voiceMaterials.value = res.list
    } else {
      voiceMaterials.value = []
    }
  } catch (error) {
    console.error('获取音频素材失败:', error)
    voiceMaterials.value = []
  } finally {
    materialLoading.value = false
  }
}

// 获取文档素材列表
const fetchDocumentMaterials = async () => {
  try {
    materialLoading.value = true
    const params = {
      pageNo: 1,
      pageSize: 100
    }
    const res = await getDocumentList(params)

    // 兼容多种返回格式
    if (Array.isArray(res)) {
      documentMaterials.value = res
    } else if (res && Array.isArray(res.data)) {
      documentMaterials.value = res.data
    } else if (res && res.data && Array.isArray(res.data.list)) {
      documentMaterials.value = res.data.list
    } else if (res && Array.isArray(res.list)) {
      documentMaterials.value = res.list
    } else {
      documentMaterials.value = []
    }
  } catch (error) {
    console.error('获取文档素材失败:', error)
    documentMaterials.value = []
  } finally {
    materialLoading.value = false
  }
}

// 素材类型变化处理方法
const handleMaterialTypeChange = async (materialType: string) => {
  // 清空当前选择的素材
  selectedMaterial.value = ''

  // 根据素材类型加载对应的素材数据
  switch (materialType) {
    case '视频':
      if (videoMaterials.value.length === 0) {
        await fetchVideoMaterials()
      }
      break
    case '音频':
      if (voiceMaterials.value.length === 0) {
        await fetchVoiceMaterials()
      }
      break
    case '文档':
      if (documentMaterials.value.length === 0) {
        await fetchDocumentMaterials()
      }
      break
  }
}

// ==================== 附件管理方法 ====================

// 获取课程附件列表
const fetchAttachmentList = async (courseId: number) => {
  try {
    attachmentsLoading.value = true
    console.log('调用获取附件列表接口，courseId:', courseId)
    const result: any = await DigitalAssetApi.getAttachmentList(courseId)
    console.log('获取附件列表接口返回:', result)

    // 根据实际返回的数据格式处理
    let attachments: any[] = []

    if (result && Array.isArray(result)) {
      // 如果 request 封装已经提取了 data 部分，直接返回数组
      attachments = result
    } else if (result && result.data && result.data.list && Array.isArray(result.data.list)) {
      // 完整响应格式：{code: 0, data: {list: [...]}, msg: ""}
      attachments = result.data.list
    } else if (result && result.list && Array.isArray(result.list)) {
      // 备用格式：{list: [...]}
      attachments = result.list
    } else {
      console.warn('附件列表数据格式不正确:', result)
      attachments = []
    }

    console.log('处理后的附件列表:', attachments)
    attachmentList.value = attachments
  } catch (error) {
    console.error('获取附件列表失败:', error)
    ElMessage.error('获取附件列表失败，请重试')
    attachmentList.value = []
  } finally {
    attachmentsLoading.value = false
  }
}

// 从素材库添加附件
async function addMaterial() {
  console.log('addMaterial 方法被调用')
  console.log('selectedMaterial.value:', selectedMaterial.value)
  console.log('form.id:', form.id)

  if (!selectedMaterial.value) {
    console.log('未选择素材，显示警告')
    ElMessage.warning('请选择要添加的素材')
    return
  }

  if (!form.id) {
    console.log('课程ID不存在，无法添加附件')
    ElMessage.error('课程ID不存在，无法添加附件')
    return
  }

  // 查找选中的素材信息
  const selectedMaterialInfo = filteredMaterialOptions.value.find(
    (item) => item.value === selectedMaterial.value
  )
  if (!selectedMaterialInfo) {
    console.log('找不到选中的素材信息')
    ElMessage.error('找不到选中的素材信息')
    return
  }

  console.log('选中的素材信息:', selectedMaterialInfo)

  // 检查是否已经添加过该附件
  const existingAttachment = attachmentList.value.find(
    (attachment) => attachment.attachmentName === selectedMaterialInfo.value
  )
  if (existingAttachment) {
    console.log('该附件已存在')
    ElMessage.info('该附件已添加')
    return
  }

  try {
    const params: AddAttachmentParams = {
      courseId: form.id,
      attachmentName: selectedMaterialInfo.value,
      attachmentType: selectedMaterialInfo.type,
      fileUrl: selectedMaterialInfo.url,
      fileSize: selectedMaterialInfo.size
    }

    console.log('准备调用新增附件接口')
    console.log('请求参数:', params)
    console.log('DigitalAssetApi.addAttachment 方法:', DigitalAssetApi.addAttachment)

    const result = await DigitalAssetApi.addAttachment(params)
    console.log('新增附件接口调用成功，返回结果:', result)
    ElMessage.success('附件添加成功')

    // 刷新附件列表
    console.log('开始刷新附件列表，课程ID:', form.id)
    await fetchAttachmentList(form.id)

    // 清空选择
    selectedMaterial.value = ''
    console.log('已清空素材选择')
  } catch (error: any) {
    console.error('添加附件失败，详细错误信息:', error)
    console.error('错误堆栈:', error?.stack)
    ElMessage.error('添加附件失败，请重试')
  }
}

// 删除附件
async function removeAttachment(attachment: CourseAttachment) {
  if (!attachment.id) {
    ElMessage.error('附件ID不存在，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除附件"${attachment.attachmentName}"吗？删除后无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    console.log('调用删除附件接口，附件ID:', attachment.id)
    await DigitalAssetApi.deleteAttachment(attachment.id)
    console.log('删除附件接口调用成功')
    ElMessage.success('附件删除成功')

    // 刷新附件列表
    if (form.id) {
      await fetchAttachmentList(form.id)
    }
  } catch (error) {
    if (error === 'cancel') {
      return
    }
    console.error('删除附件失败:', error)
    ElMessage.error('删除附件失败，请重试')
  }
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 下载附件
function downloadAttachment(attachment: CourseAttachment) {
  if (attachment.fileUrl) {
    window.open(attachment.fileUrl, '_blank')
  } else {
    ElMessage.error('文件URL不存在')
  }
}

// 获取课程详情
const fetchCourseDetail = async (id: number) => {
  try {
    loading.value = true
    const result = await DigitalAssetApi.getCourseDetail(id)

    // 填充表单数据
    Object.assign(form, {
      id: result.id,
      name: result.name,
      teachType: result.teachType,
      coverUrl: result.coverUrl || '',
      category: result.category,
      status: result.status,
      teacherId: result.teacherId,
      teacherName: result.teacherName || '',
      businessModule: result.businessModule || '',
      merchant: result.merchant,
      merchantName: result.merchantName || '',
      description: result.description || '',
      location: result.location || '',
      schedule: result.schedule || '',
      totalSeats: result.totalSeats,
      enrolledCount: result.enrolledCount || 0
    })
  } catch (error) {
    console.error('获取课程详情失败:', error)
    ElMessage.error('获取课程详情失败，请重试')
  } finally {
    loading.value = false
  }
}

// 更新课程信息
const updateCourse = async () => {
  try {
    loading.value = true

    const updateData: DigitalAssetCourse = {
      id: form.id!,
      name: form.name,
      teachType: form.teachType,
      coverUrl: form.coverUrl || undefined,
      category: form.category,
      status: form.status,
      teacherId: form.teacherId || undefined,
      teacherName: form.teacherName || undefined,
      businessModule: form.businessModule || undefined,
      merchant: form.merchant || undefined,
      merchantName: form.merchantName || undefined,
      description: form.description || undefined,
      location: form.location || undefined,
      schedule: form.schedule || undefined,
      totalSeats: form.totalSeats || undefined,
      enrolledCount: form.enrolledCount || 0
    }

    await DigitalAssetApi.updateCourse(updateData)
    ElMessage.success('课程更新成功')
  } catch (error) {
    console.error('更新课程失败:', error)
    ElMessage.error('更新课程失败，请重试')
  } finally {
    loading.value = false
  }
}

function onCancel() {
  emit('goBack')
}

async function onSubmit() {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    await updateCourse()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

/** 获取业务模块字典数据 */
const fetchBusinessModuleOptions = async () => {
  try {
    const res = await getDictDataPage({ dictType: 'business_module', pageNo: 1, pageSize: 100 })
    businessModuleOptions.value = (res?.list || []).map((item) => ({
      label: item.label,
      value: item.value
    }))
  } catch (error) {
    console.error('获取业务模块字典数据失败:', error)
  }
}

/** 获取讲师列表数据 */
const fetchTeacherOptions = async () => {
  try {
    const res = await request.get({ url: '/publicbiz/teacher/list' })
    console.log('讲师接口返回数据:', res)

    // 根据接口文档，数据直接在res中，不是res.data
    if (res && Array.isArray(res)) {
      teacherOptions.value = res.map((item: any) => ({
        id: item.id,
        name: item.name,
        label: item.name,
        value: item.id
      }))
      console.log('处理后的讲师选项:', teacherOptions.value)
    } else if (res.data && Array.isArray(res.data)) {
      // 如果数据在res.data中
      teacherOptions.value = res.data.map((item: any) => ({
        id: item.id,
        name: item.name,
        label: item.name,
        value: item.id
      }))
      console.log('处理后的讲师选项:', teacherOptions.value)
    } else {
      console.log('接口返回数据格式不符合预期:', res)
    }
  } catch (error) {
    console.error('获取讲师列表失败:', error)
  }
}

/** 获取商户列表数据 */
const fetchMerchantOptions = async () => {
  try {
    const res = await getAppList()
    console.log('支付应用接口返回数据:', res)
    console.log('res.code:', res?.code)
    console.log('res.data:', res?.data)
    console.log('Array.isArray(res.data):', Array.isArray(res?.data))

    // 检查数据结构 - 直接判断是否为数组
    if (Array.isArray(res)) {
      console.log('数据直接在res中，数组长度:', res.length)
      // 直接使用应用数据作为商户选项
      merchantOptions.value = res.map((app: any) => ({
        id: app.id,
        name: app.name,
        label: app.name,
        value: app.id
      }))
      console.log('处理后的商户选项:', merchantOptions.value)
    } else {
      console.log('支付应用接口返回数据格式不符合预期:', res)
    }
  } catch (error) {
    console.error('获取商户列表失败:', error)
  }
}

// 页面加载时获取课程详情
onMounted(() => {
  console.log('ManagementCourse onMounted, props.course:', props.course)
  fetchBusinessModuleOptions()
  fetchTeacherOptions()
  fetchMerchantOptions()
  // 预加载素材数据
  fetchVideoMaterials()
  fetchVoiceMaterials()
  fetchDocumentMaterials()

  if (props.course?.id) {
    console.log('调用 fetchCourseDetail，课程ID:', props.course.id)
    fetchCourseDetail(props.course.id)
    fetchAttachmentList(props.course.id)
  } else {
    console.log('props.course 或 props.course.id 不存在')
  }
})

// 监听 props 变化
watch(
  () => props.course,
  (newCourse) => {
    console.log('ManagementCourse watch props.course 变化:', newCourse)
    if (newCourse?.id) {
      console.log('调用 fetchCourseDetail，课程ID:', newCourse.id)
      fetchCourseDetail(newCourse.id)
      fetchAttachmentList(newCourse.id)
    } else {
      console.log('newCourse 或 newCourse.id 不存在')
    }
  },
  { immediate: true }
)

function goBack() {
  emit('goBack')
}
</script>

<style scoped>
.course-form {
  max-width: 900px;
}
.attachment-section {
  max-width: 900px;
  margin: 0 auto;
}
.attachment-section {
  padding: 16px;
}

.attachment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.attachment-title {
  font-weight: bold;
  font-size: 16px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}
</style>
