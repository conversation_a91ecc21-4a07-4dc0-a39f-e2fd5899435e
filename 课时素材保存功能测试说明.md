# 课时素材保存功能完善说明

## 🎯 功能概述

已成功完善课时保存功能，确保用户选择的关联素材信息能够正确保存到数据库中，包括素材ID和素材URL的完整映射。

## 🔧 技术实现要点

### 1. 素材选择处理

#### 关联素材选择器增强
```vue
<el-select
  v-model="currentLesson.materialName"
  @change="handleMaterialChange"
  :loading="materialLoading"
  :disabled="!currentLesson.lessonType"
>
  <el-option
    v-for="item in filteredMaterialOptions"
    :key="item.value"
    :label="item.label"
    :value="item.value"
    :data-id="item.id"
    :data-url="item.url"
  />
</el-select>
```

#### 素材变化处理方法
```typescript
const handleMaterialChange = (materialName: string) => {
  if (!materialName) {
    // 清空素材信息
    currentLesson.materialId = ''
    currentLesson.materialFileUrl = ''
    return
  }

  // 根据当前课时类型找到对应的素材数据
  let materials: any[] = []
  switch (currentLesson.lessonType) {
    case '视频': materials = videoMaterials.value; break
    case '音频': materials = voiceMaterials.value; break
    case '文档': materials = documentMaterials.value; break
  }

  // 查找选中的素材
  const selectedMaterial = materials.find(item => {
    const name = item.name || item.documentName
    return name === materialName
  })

  if (selectedMaterial) {
    // 设置素材ID和URL
    currentLesson.materialId = selectedMaterial.id.toString()
    
    // 根据素材类型设置正确的URL字段
    if (currentLesson.lessonType === '文档') {
      currentLesson.materialFileUrl = selectedMaterial.documentUrl || ''
    } else {
      // 视频和音频都使用url字段
      currentLesson.materialFileUrl = selectedMaterial.url || ''
    }
  }
}
```

### 2. 数据结构完善

#### 课时数据类型定义
```typescript
lessons: Array<{
  id?: number
  title: string
  lessonType: string
  isFree: boolean
  materialId?: string        // 新增：素材ID
  materialName: string
  materialFileUrl?: string   // 素材文件URL
}>
```

#### 当前编辑课时对象
```typescript
const currentLesson = reactive({
  id: undefined as number | undefined,
  courseId: 0,
  chapterId: 0,
  title: '',
  lessonType: '视频',
  isFree: false,
  materialId: '',           // 素材ID
  materialName: '',         // 素材名称
  materialFileUrl: '',      // 素材文件URL
  sortOrder: 0
})
```

### 3. 保存API调用

#### 新增课时参数
```typescript
const params: AddLessonParams = {
  courseId: currentLesson.courseId,
  chapterId: currentLesson.chapterId,
  title: currentLesson.title,
  lessonType: currentLesson.lessonType,
  isFree: currentLesson.isFree,
  materialId: currentLesson.materialId || undefined,
  materialName: currentLesson.materialName || undefined,
  materialFileUrl: currentLesson.materialFileUrl || undefined,
  sortOrder: currentLesson.sortOrder
}
```

#### 更新课时参数
```typescript
const params: UpdateLessonParams = {
  id: currentLesson.id!,
  courseId: currentLesson.courseId,
  chapterId: currentLesson.chapterId,
  title: currentLesson.title,
  lessonType: currentLesson.lessonType,
  isFree: currentLesson.isFree,
  materialId: currentLesson.materialId || undefined,
  materialName: currentLesson.materialName || undefined,
  materialFileUrl: currentLesson.materialFileUrl || undefined,
  sortOrder: currentLesson.sortOrder
}
```

## 🔄 数据流程

### 1. 选择素材流程
1. 用户选择课时类型（视频/音频/文档）
2. 系统自动加载对应类型的素材列表
3. 用户从关联素材下拉框中选择具体素材
4. 系统自动设置：
   - `materialId`: 素材的数据库ID
   - `materialName`: 素材名称
   - `materialFileUrl`: 素材文件URL（根据类型使用不同字段）

### 2. URL字段映射规则
- **视频素材**: `materialFileUrl = selectedMaterial.url`
- **音频素材**: `materialFileUrl = selectedMaterial.url`
- **文档素材**: `materialFileUrl = selectedMaterial.documentUrl`

### 3. 保存到数据库
调用API时，将前端数据映射到数据库字段：
- `materialId` → `material_id` 字段
- `materialFileUrl` → `material_file_url` 字段

## 🧪 测试步骤

### 测试场景1: 新增课时
1. 进入课程大纲页面
2. 点击"添加课时"
3. 选择课时类型为"视频"
4. 从关联素材中选择一个视频
5. 点击保存
6. 验证：课时保存成功，包含正确的素材ID和URL

### 测试场景2: 编辑课时
1. 编辑已有课时
2. 更改课时类型为"音频"
3. 选择新的音频素材
4. 点击保存
5. 验证：课时更新成功，素材信息正确更新

### 测试场景3: 清空素材
1. 编辑课时
2. 将关联素材选择清空
3. 点击保存
4. 验证：素材ID和URL字段被正确清空

## ✅ 预期效果

### 数据库记录示例
```sql
-- 课时表记录
INSERT INTO course_lesson (
  course_id,
  chapter_id,
  title,
  lesson_type,
  is_free,
  material_id,
  material_name,
  material_file_url,
  sort_order
) VALUES (
  1,                                    -- 课程ID
  1,                                    -- 章节ID
  '课程导论',                           -- 课时标题
  '视频',                               -- 课时类型
  1,                                    -- 是否免费
  '123',                                -- 素材ID
  '课程导论.mp4',                       -- 素材名称
  'https://example.com/video/123.mp4',  -- 素材文件URL
  1                                     -- 排序
);
```

### 功能验证点
✅ 素材ID正确保存到material_id字段
✅ 素材URL正确保存到material_file_url字段
✅ 不同素材类型的URL字段映射正确
✅ 编辑课时时能正确回填素材信息
✅ 清空素材选择时能正确清理相关字段
✅ 课时类型变化时自动清空不匹配的素材

## 🎉 功能优势

1. **数据完整性**: 确保素材关联信息完整保存
2. **类型安全**: 严格的类型检查，避免数据错误
3. **用户友好**: 自动处理素材选择和清理逻辑
4. **扩展性强**: 易于支持新的素材类型
5. **维护性好**: 清晰的数据流和错误处理

这个完善的保存功能为后续的课时播放和素材管理功能奠定了坚实的数据基础！
