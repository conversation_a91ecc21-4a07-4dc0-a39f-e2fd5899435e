# 课时类型与关联素材二级联动功能实现说明

## 🎯 功能概述

在线上课程管理系统中，成功实现了课时类型与关联素材的二级联动功能。当用户在课时编辑弹窗中选择不同的课时类型时，关联素材下拉框会自动筛选并显示对应类型的素材，提供了更好的用户体验。

## 🔧 技术实现

### 1. 核心文件修改
- **文件路径**: `src/views/infra/ResourceCenter/DigitalAsset/components/ManagementCourseForOnline.vue`
- **修改内容**: 添加了素材联动逻辑和API调用

### 2. 主要功能点

#### 📋 课时类型选择器增强
```vue
<el-select
  v-model="currentLesson.lessonType"
  placeholder="请选择课时类型"
  style="width: 100%"
  @change="handleLessonTypeChange"
>
  <el-option label="视频" value="视频" />
  <el-option label="文档" value="文档" />
  <el-option label="音频" value="音频" />
</el-select>
```

#### 📋 关联素材选择器增强
```vue
<el-select
  v-model="currentLesson.materialName"
  placeholder="请选择关联素材"
  style="width: 100%"
  :loading="materialLoading"
  :disabled="!currentLesson.lessonType"
>
  <el-option
    v-for="item in filteredMaterialOptions"
    :key="item.value"
    :label="item.label"
    :value="item.value"
  />
</el-select>
```

### 3. 核心逻辑实现

#### 🔄 联动处理方法
```typescript
const handleLessonTypeChange = async (lessonType: string) => {
  // 清空当前选择的素材
  currentLesson.materialName = ''
  currentLesson.materialFileUrl = ''
  
  // 根据课时类型加载对应的素材数据
  switch (lessonType) {
    case '视频':
      if (videoMaterials.value.length === 0) {
        await fetchVideoMaterials()
      }
      break
    case '音频':
      if (voiceMaterials.value.length === 0) {
        await fetchVoiceMaterials()
      }
      break
    case '文档':
      if (documentMaterials.value.length === 0) {
        await fetchDocumentMaterials()
      }
      break
  }
}
```

#### 🎯 智能过滤计算属性
```typescript
const filteredMaterialOptions = computed(() => {
  const lessonType = currentLesson.lessonType
  let materials: any[] = []
  
  switch (lessonType) {
    case '视频':
      materials = videoMaterials.value
      break
    case '音频':
      materials = voiceMaterials.value
      break
    case '文档':
      materials = documentMaterials.value
      break
    default:
      materials = []
  }
  
  return materials.map((item) => ({
    label: `${item.name || item.documentName} (${item.description || '素材文件'})`,
    value: item.name || item.documentName,
    id: item.id,
    url: item.url || item.documentUrl
  }))
})
```

## 🚀 使用流程

### 步骤1: 进入课程管理
1. 打开线上课程管理页面
2. 选择"课程大纲"标签页
3. 点击"添加课时"或"编辑"现有课时

### 步骤2: 体验联动功能
1. **选择"视频"类型**
   - 关联素材下拉框自动显示所有视频素材
   - 素材名称格式：`视频名称 (描述信息)`

2. **选择"音频"类型**
   - 关联素材下拉框自动显示所有音频素材
   - 包含音频时长等详细信息

3. **选择"文档"类型**
   - 关联素材下拉框自动显示所有文档素材
   - 支持各种文档格式（PDF、Word、Excel等）

## ✨ 功能特色

### 🎯 智能联动
- **实时响应**: 课时类型变化时立即更新素材选项
- **数据清理**: 切换类型时自动清空不匹配的素材选择
- **懒加载**: 只在需要时加载对应类型的素材数据

### 🔄 性能优化
- **预加载**: 页面初始化时预加载所有类型素材
- **缓存机制**: 已加载的素材数据会被缓存，避免重复请求
- **加载状态**: 提供loading状态指示，提升用户体验

### 🛡️ 用户体验
- **防误操作**: 未选择课时类型时，关联素材选择器处于禁用状态
- **清晰标识**: 素材选项包含名称和描述信息，便于识别
- **兼容性**: 支持多种API返回格式，确保稳定性

## 🔍 技术细节

### API集成
```typescript
// 导入素材库相关API
import { getVideoList } from '@/api/infra/materiallibrary/materialVideo'
import { getVoiceList } from '@/api/infra/materiallibrary/materialVoice'
import { getDocumentList } from '@/api/infra/materiallibrary/materialDocument'
```

### 数据格式兼容
```typescript
// 兼容多种返回格式
if (Array.isArray(res)) {
  materials.value = res
} else if (res && Array.isArray(res.data)) {
  materials.value = res.data
} else if (res && res.data && Array.isArray(res.data.list)) {
  materials.value = res.data.list
} else if (res && Array.isArray(res.list)) {
  materials.value = res.list
} else {
  materials.value = []
}
```

## 🎉 实现效果

✅ **完美联动**: 课时类型与关联素材实现无缝联动
✅ **用户友好**: 界面直观，操作简单
✅ **性能优秀**: 响应迅速，加载流畅
✅ **数据准确**: 确保素材类型与课时类型完全匹配
✅ **扩展性强**: 易于添加新的素材类型支持

这个功能大大提升了课程创建的效率和准确性，为用户提供了更好的操作体验！
