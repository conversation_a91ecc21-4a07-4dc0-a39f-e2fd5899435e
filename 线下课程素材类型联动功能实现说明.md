# 线下课程管理 - 素材类型与素材选择二级联动功能实现

## 🎯 功能概述

成功在线下课程管理系统中实现了素材类型与素材选择的二级联动功能，用户可以先选择素材类型（视频/音频/文档），然后从对应类型的素材列表中选择具体素材，提供更精准的素材选择体验。

## 🔧 技术实现

### 1. 界面改造

#### 原有界面
```vue
<!-- 原有的单一素材选择器 -->
<el-select v-model="selectedMaterial" placeholder="请选择素材">
  <el-option v-for="item in materialOptions" ... />
</el-select>
```

#### 改造后界面
```vue
<!-- 新增素材类型选择器 + 联动素材选择器 -->
<el-row :gutter="16" align="middle" style="margin-bottom: 16px">
  <el-col :span="6">
    <el-select 
      v-model="selectedMaterialType" 
      placeholder="请选择素材类型"
      @change="handleMaterialTypeChange"
    >
      <el-option
        v-for="item in materialTypeOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </el-col>
  <el-col :span="12">
    <el-select 
      v-model="selectedMaterial" 
      placeholder="请选择素材"
      :loading="materialLoading"
      :disabled="!selectedMaterialType"
    >
      <el-option
        v-for="item in filteredMaterialOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </el-col>
  <el-col :span="6">
    <el-button type="primary" @click="addMaterial">添加附件</el-button>
  </el-col>
</el-row>
```

### 2. 数据结构设计

#### 素材相关响应式数据
```typescript
// 素材相关数据
const materialLoading = ref(false)
const selectedMaterialType = ref('')
const videoMaterials = ref<any[]>([])
const voiceMaterials = ref<any[]>([])
const documentMaterials = ref<any[]>([])

// 素材类型选项
const materialTypeOptions = [
  { label: '视频', value: '视频' },
  { label: '音频', value: '音频' },
  { label: '文档', value: '文档' }
]
```

#### 智能过滤计算属性
```typescript
const filteredMaterialOptions = computed(() => {
  const materialType = selectedMaterialType.value
  let materials: any[] = []
  
  switch (materialType) {
    case '视频': materials = videoMaterials.value; break
    case '音频': materials = voiceMaterials.value; break
    case '文档': materials = documentMaterials.value; break
    default: materials = []
  }
  
  return materials.map(item => ({
    label: `${item.name || item.documentName} (${item.description || '素材文件'})`,
    value: item.name || item.documentName,
    id: item.id,
    url: item.url || item.documentUrl,
    type: materialType,
    size: item.fileSize || 0
  }))
})
```

### 3. API集成

#### 素材库API导入
```typescript
// 导入素材库相关API
import { getVideoList } from '@/api/infra/materiallibrary/materialVideo'
import { getVoiceList } from '@/api/infra/materiallibrary/materialVoice'
import { getDocumentList } from '@/api/infra/materiallibrary/materialDocument'
```

#### 素材获取方法
```typescript
// 获取视频素材列表
const fetchVideoMaterials = async () => {
  try {
    materialLoading.value = true
    const params = { pageNo: 1, pageSize: 100 }
    const res = await getVideoList(params)
    
    // 兼容多种返回格式
    if (Array.isArray(res)) {
      videoMaterials.value = res
    } else if (res && res.data && Array.isArray(res.data.list)) {
      videoMaterials.value = res.data.list
    } else if (res && Array.isArray(res.list)) {
      videoMaterials.value = res.list
    } else {
      videoMaterials.value = []
    }
  } catch (error) {
    console.error('获取视频素材失败:', error)
    videoMaterials.value = []
  } finally {
    materialLoading.value = false
  }
}
```

### 4. 联动处理逻辑

#### 素材类型变化处理
```typescript
const handleMaterialTypeChange = async (materialType: string) => {
  // 清空当前选择的素材
  selectedMaterial.value = ''
  
  // 根据素材类型加载对应的素材数据
  switch (materialType) {
    case '视频':
      if (videoMaterials.value.length === 0) {
        await fetchVideoMaterials()
      }
      break
    case '音频':
      if (voiceMaterials.value.length === 0) {
        await fetchVoiceMaterials()
      }
      break
    case '文档':
      if (documentMaterials.value.length === 0) {
        await fetchDocumentMaterials()
      }
      break
  }
}
```

## 🔄 功能流程

### 用户操作流程
1. **进入课程附件页面** → 看到素材类型和素材选择器
2. **选择素材类型** → 系统自动加载对应类型的素材列表
3. **选择具体素材** → 从筛选后的素材列表中选择
4. **添加附件** → 将选中的素材添加为课程附件

### 数据流程
```
用户选择素材类型
        ↓
系统调用对应API获取素材列表
        ↓
计算属性自动过滤素材选项
        ↓
用户从筛选后的列表中选择素材
        ↓
添加为课程附件
```

## ✨ 功能特色

### 🎯 智能联动
- **实时响应**: 素材类型变化时立即更新素材选项
- **自动清理**: 切换类型时自动清空已选素材
- **懒加载**: 只在需要时加载对应类型的素材数据

### 🛡️ 用户体验
- **防误操作**: 未选择素材类型时，素材选择器处于禁用状态
- **加载状态**: 提供loading状态指示，提升交互体验
- **信息丰富**: 素材选项包含名称和描述信息

### 🔄 性能优化
- **预加载**: 页面初始化时预加载所有类型素材
- **缓存机制**: 已加载的素材数据会被缓存
- **兼容性**: 支持多种API返回格式

## 🧪 测试验证

### 功能测试点
✅ 选择"视频"类型 → 显示视频素材列表
✅ 选择"音频"类型 → 显示音频素材列表  
✅ 选择"文档"类型 → 显示文档素材列表
✅ 切换素材类型 → 自动清空已选素材
✅ 未选择类型 → 素材选择器禁用
✅ 添加附件 → 正确获取素材信息

### 界面布局验证
- 素材类型选择器：占用6列宽度
- 素材选择器：占用12列宽度  
- 添加按钮：占用6列宽度
- 整体布局：保持与原有界面风格一致

## 🎉 实现效果

### 用户体验提升
1. **选择更精准**: 先选类型再选素材，避免在大量素材中查找
2. **操作更直观**: 清晰的两级选择流程
3. **响应更迅速**: 智能缓存和懒加载机制

### 技术价值
1. **代码复用**: 基于线上课程管理的成熟方案
2. **架构一致**: 与现有素材库API完美集成
3. **可维护性**: 清晰的代码结构和错误处理
4. **可扩展性**: 易于添加新的素材类型支持

## 📝 总结

成功在线下课程管理中实现了与线上课程管理相同的素材类型与素材选择二级联动功能，提供了：

1. **完美的用户体验** - 直观、流畅的素材选择流程
2. **可靠的技术实现** - 基于成熟的API和组件架构
3. **优秀的性能表现** - 智能缓存和按需加载
4. **强大的扩展能力** - 支持未来功能扩展

为线下课程管理系统提供了强大而可靠的素材管理能力！🚀
