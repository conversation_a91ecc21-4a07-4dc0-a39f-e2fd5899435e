<!--
  页面名称：服务套餐管理
  功能描述：管理就业服务相关的服务套餐，包括套餐列表、新增、编辑、删除等功能
-->
<template>
  <div class="service-package-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2><i class="fas fa-shopping-cart"></i> 服务套餐</h2>
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus"></i> + 添加新商品
      </el-button>
    </div>

    <!-- 标签页导航 -->
    <el-tabs v-model="activeTab" class="status-tabs" @tab-change="handleTabChange">
      <el-tab-pane name="listed">
        <template #label>
          已上架
          <span class="tab-badge">{{ listedCount }}</span>
        </template>
      </el-tab-pane>
      <el-tab-pane name="pending">
        <template #label>
          待上架
          <span class="tab-badge">{{ pendingCount }}</span>
        </template>
      </el-tab-pane>
      <el-tab-pane name="recycled">
        <template #label>
          回收站
          <span class="tab-badge">{{ recycledCount }}</span>
        </template>
      </el-tab-pane>
    </el-tabs>

    <!-- 搜索筛选区域 -->
    <div class="filter-section">
      <el-form :model="filterForm" inline>
        <el-form-item>
          <el-input
            v-model="filterForm.keyword"
            placeholder="输入商品名称或ID..."
            clearable
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item label="商品分类:">
          <el-select v-model="filterForm.category" placeholder="全部分类" clearable>
            <el-option label="全部分类" value="" />
            <el-option
              v-for="item in serviceTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 套餐列表表格 -->
    <div class="package-table">
      <el-table v-loading="loading" :data="packageList" border style="width: 100%">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID/序号" width="80" />
        <el-table-column label="套餐主图" width="120">
          <template #default="{ row }">
            <el-image :src="row.thumbnail" style="width: 80px; height: 60px" fit="cover" />
          </template>
        </el-table-column>
        <el-table-column label="套餐名称" min-width="200">
          <template #default="{ row }">
            <div>
              <div>{{ row.name }}</div>
              <div style="font-size: 12px; color: #999">ID: {{ row.id }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="分类" width="120" />
        <el-table-column label="价格(元)" width="120">
          <template #default="{ row }">
            <span style="color: #e74c3c; font-weight: bold">¥{{ row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column label="套餐类型" width="150">
          <template #default="{ row }">
            <el-tag :type="getPackageTypeTag(row.packageType)" size="small">
              {{ getPackageTypeText(row.packageType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="taskSplitRule" label="任务拆分规则" min-width="200" />
        <el-table-column label="特色标签" min-width="200">
          <template #default="{ row }">
            <div class="feature-tags-display">
              <el-tag
                v-for="feature in row.featureList"
                :key="feature.id"
                size="small"
                style="margin-right: 4px; margin-bottom: 4px"
              >
                {{ feature.featureName }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" size="small" @click="handleToggleStatus(row)">
              {{ getToggleStatusText(row.status) }}
            </el-button>
            <el-button type="text" size="small" style="color: #f56c6c" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <span class="total-count">共{{ pagination.total }}条</span>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      :title="drawerTitle"
      direction="rtl"
      size="900px"
      :before-close="handleCloseDrawer"
      class="package-form-drawer"
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
        <!-- 基础信息 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-info"></i>
            <span>基础信息</span>
          </div>

          <el-form-item label="套餐名称 *" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="例如:金牌月嫂|26天贴心陪护"
              style="width: 100%"
            />
            <div class="form-tip">将显示在小程序套餐卡片的标题位置</div>
          </el-form-item>

          <el-form-item label="服务分类 *" prop="category">
            <el-select v-model="formData.category" placeholder="请选择服务分类" style="width: 100%">
              <el-option
                v-for="item in serviceTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="form-tip">对应小程序套餐选择页的分类Tab</div>
          </el-form-item>

          <el-form-item label="套餐主图 *" prop="thumbnail">
            <el-upload
              class="image-uploader"
              :show-file-list="false"
              :before-upload="beforeImageUpload"
              :http-request="handleImageUpload"
              :on-success="onImageSuccess"
            >
              <img v-if="formData.thumbnail" :src="formData.thumbnail" class="upload-image" />
              <el-icon v-else class="upload-icon"><Plus /></el-icon>
              <div class="upload-text">点击上传套餐主图</div>
              <div class="upload-hint">建议尺寸160×160px</div>
            </el-upload>
            <div class="form-tip">显示在小程序套餐卡片左侧,建议使用清晰的服务场景图</div>
          </el-form-item>

          <el-form-item label="套餐轮播图 选填" prop="carouselList">
            <el-upload
              class="carousel-uploader"
              :show-file-list="false"
              :before-upload="beforeImageUpload"
              :http-request="handleCarouselImageUpload"
            >
              <el-icon class="upload-icon"><Plus /></el-icon>
              <div class="upload-text">添加轮播图</div>
            </el-upload>
            <div class="form-tip">用于服务详情页顶部轮播展示,最多5张</div>
          </el-form-item>
        </div>

        <!-- 价格与规格信息 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-price-tag"></i>
            <span>价格与规格信息</span>
          </div>

          <el-form-item label="套餐价格 *" prop="price">
            <el-input-number
              v-model="formData.price"
              :min="0"
              :precision="2"
              style="width: 100%"
              placeholder="如: 8800"
            />
            <div class="form-tip">显示在小程序套餐卡片的价格位置,如:¥8,800</div>
          </el-form-item>

          <el-form-item label="价格单位 *" prop="unit">
            <el-select v-model="formData.unit" placeholder="请选择单位" style="width: 100%">
              <el-option
                v-for="item in priceUnitOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="form-tip">显示在小程序价格后面,如:¥8,800/次</div>
          </el-form-item>

          <el-form-item label="服务时长 *" prop="serviceDuration">
            <el-input
              v-model="formData.serviceDuration"
              placeholder="如: 4小时、26天、90天"
              style="width: 100%"
            />
            <div class="form-tip">参考时长,用于套餐规格描述</div>
          </el-form-item>
        </div>

        <!-- 套餐类型与特色标签 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-collection"></i>
            <span>套餐类型与特色标签</span>
          </div>

          <el-form-item label="套餐类型 *" prop="packageType">
            <el-select
              v-model="formData.packageType"
              placeholder="请选择套餐类型"
              style="width: 100%"
            >
              <el-option
                v-for="item in packageTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="form-tip">
              长周期套餐: 如月嫂26天服务,显示"长周期套餐"标签,适合连续性服务<br />
              次数次卡套餐: 如4次收纳整理,显示"次数次卡套餐"标签,适合灵活预约服务
            </div>
          </el-form-item>

          <el-form-item label="特色标签 *" prop="featureList">
            <div class="feature-input">
              <el-input
                v-model="newFeature"
                placeholder="输入特色标签,如:24小时服务"
                style="width: 300px"
                @keyup.enter="addFeature"
              />
              <el-button type="primary" @click="addFeature">添加</el-button>
            </div>
            <div class="feature-tags">
              <el-tag
                v-for="(feature, index) in formData.featureList"
                :key="index"
                closable
                @close="removeFeature(index)"
                style="margin-right: 8px; margin-bottom: 8px"
              >
                {{ feature.featureName }}
              </el-tag>
            </div>
            <div class="form-tip">
              推荐标签:
              <el-tag
                v-for="tag in recommendedTags"
                :key="tag"
                size="small"
                style="margin-right: 5px; cursor: pointer"
                @click="addRecommendedTag(tag)"
              >
                {{ tag }}
              </el-tag>
            </div>
          </el-form-item>
        </div>

        <!-- 服务详情 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-document"></i>
            <span>服务详情</span>
          </div>

          <el-form-item label="服务描述 *" prop="serviceDescription">
            <el-input
              v-model="formData.serviceDescription"
              type="textarea"
              :rows="3"
              placeholder="简要描述服务内容和特色,如:专业月嫂技能培训,涵盖新生儿护理、产妇护理、月子餐制作等核心技能"
              style="width: 100%"
            />
            <div class="form-tip">显示在小程序服务详情页,建议100-200字</div>
          </el-form-item>

          <el-form-item label="详细服务内容" prop="serviceDetails">
            <div class="rich-editor">
              <div class="editor-toolbar">
                <el-button size="small" @click="formatText('bold')"><strong>B</strong></el-button>
                <el-button size="small" @click="formatText('italic')"><em>I</em></el-button>
                <el-button size="small" @click="formatText('underline')"><u>U</u></el-button>
                <el-button size="small" @click="formatText('list')">•</el-button>
              </div>
              <el-input
                v-model="formData.serviceDetails"
                type="textarea"
                :rows="8"
                placeholder="详细描述服务范围、服务标准、服务流程等"
                style="width: 100%"
              />
            </div>
            <div class="form-tip">用于小程序详情页的图文详情Tab</div>
          </el-form-item>
        </div>

        <!-- 购买须知 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-warning"></i>
            <span>购买须知</span>
          </div>

          <el-form-item label="购买须知" prop="purchaseNotice">
            <el-input
              v-model="formData.purchaseNotice"
              type="textarea"
              :rows="4"
              placeholder="用户下单前需要了解的重要信息,如预约规则、取消政策等"
              style="width: 100%"
            />
            <div class="form-tip">重要提醒和注意事项</div>
          </el-form-item>
        </div>

        <!-- 预约配置 -->
        <div class="form-section">
          <div class="section-header">
            <i class="el-icon-date"></i>
            <span>预约配置</span>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="预约时间范围 *" prop="advanceBookingDays">
                <el-select
                  v-model="formData.advanceBookingDays"
                  placeholder="请选择预约时间范围"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in appointmentTimeRangeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div class="form-tip">如"提前7天预约"、"提前3天预约"</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="时间选择模式 *" prop="timeSelectionMode">
                <el-select
                  v-model="formData.timeSelectionMode"
                  placeholder="请选择时间选择模式"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in timeSelectionModeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div class="form-tip">选择"固定时间"或"灵活时间"</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="预约模式 *" prop="appointmentMode">
                <el-select
                  v-model="formData.appointmentMode"
                  placeholder="请选择预约模式"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in appointmentModeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div class="form-tip"
                  >长周期套餐选择"开始日期预约",次数次卡套餐选择"一次性预约全部服务次数"</div
                >
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="服务开始时间 *" prop="serviceStartTime">
                <el-select
                  v-model="formData.serviceStartTime"
                  placeholder="请选择服务开始时间"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in serviceStartTimeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div class="form-tip">如"下单后3天内开始"、"指定日期开始"</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="地址设置 *" prop="addressSetting">
                <el-select
                  v-model="formData.addressSetting"
                  placeholder="请选择地址设置"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in addressSettingOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div class="form-tip">选择"固定地址"或"可变更地址"</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商品状态:" prop="status">
                <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
                  <el-option
                    v-for="item in productStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <template #footer>
        <div class="drawer-footer">
          <el-button @click="handleCloseDrawer">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">保存更新</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  getServicePackageList,
  createServicePackage,
  updateServicePackage,
  deleteServicePackage,
  getServicePackageDetail,
  updateSingleServicePackageStatus
} from '@/api/mall/employment/servicePackage'
// 导入数据字典接口
import { getDictDataPage } from '@/api/system/dict/dict.data'
// 导入文件上传接口
import { updateFile } from '@/api/infra/file'
import type {
  ServicePackage,
  CreateServicePackageParams,
  UpdateServicePackageParams,
  ServicePackageCarousel,
  ServicePackageFeature
} from '@/api/mall/employment/servicePackage'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const drawerVisible = ref(false)
const isEdit = ref(false)
const currentId = ref<number>(0)
const activeTab = ref('listed')
const newFeature = ref('')

// 数据字典选项
const serviceTypeOptions = ref<any[]>([])
const priceUnitOptions = ref<any[]>([])
const packageTypeOptions = ref<any[]>([])
const appointmentTimeRangeOptions = ref<any[]>([])
const serviceStartTimeOptions = ref<any[]>([])
const timeSelectionModeOptions = ref<any[]>([])
const addressSettingOptions = ref<any[]>([])
const appointmentModeOptions = ref<any[]>([])
const productStatusOptions = ref<any[]>([])

const filterForm = reactive({
  keyword: '',
  category: ''
})

const packageList = ref<ServicePackage[]>([])
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const formRef = ref<FormInstance>()
const formData = reactive({
  name: '',
  category: '',
  thumbnail: '',
  carouselList: [] as ServicePackageCarousel[],
  price: 0,
  unit: '次',
  serviceDuration: '',
  packageType: 'count-card',
  taskSplitRule: '',
  featureList: [] as ServicePackageFeature[],
  serviceDescription: '',
  serviceDetails: '',
  serviceProcess: '',
  purchaseNotice: '',
  status: 'active',
  advanceBookingDays: 1,
  timeSelectionMode: 'fixed',
  appointmentMode: 'start-date',
  serviceStartTime: 'within-3-days',
  addressSetting: 'fixed',
  maxBookingDays: 30,
  cancellationPolicy: ''
})

// 推荐标签
const recommendedTags = ['24小时服务', '专业认证', '上门服务', '灵活预约', '品质保障']

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入套餐名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择服务分类', trigger: 'change' }],
  thumbnail: [{ required: true, message: '请上传套餐主图', trigger: 'change' }],
  price: [{ required: true, message: '请输入套餐价格', trigger: 'blur' }],
  unit: [{ required: true, message: '请选择价格单位', trigger: 'change' }],
  serviceDuration: [{ required: true, message: '请输入服务时长', trigger: 'blur' }],
  packageType: [{ required: true, message: '请选择套餐类型', trigger: 'change' }],
  featureList: [{ required: true, message: '请添加特色标签', trigger: 'change' }],
  serviceDescription: [{ required: true, message: '请输入服务描述', trigger: 'blur' }],
  advanceBookingDays: [{ required: true, message: '请选择预约时间范围', trigger: 'change' }],
  timeSelectionMode: [{ required: true, message: '请选择时间选择模式', trigger: 'change' }],
  appointmentMode: [{ required: true, message: '请选择预约模式', trigger: 'change' }],
  serviceStartTime: [{ required: true, message: '请选择服务开始时间', trigger: 'change' }],
  addressSetting: [{ required: true, message: '请选择地址设置', trigger: 'change' }]
}

// 计算属性
const drawerTitle = computed(() => {
  return isEdit.value ? '编辑套餐' : '添加新套餐'
})

// 计算各状态的数量
const listedCount = computed(() => {
  return packageList.value.filter((item) => item.status === 'active').length
})

const pendingCount = computed(() => {
  return packageList.value.filter((item) => item.status === 'pending').length
})

const recycledCount = computed(() => {
  return packageList.value.filter((item) => item.status === 'deleted').length
})

// 加载数据字典选项
const loadDictOptions = async () => {
  try {
    // 服务分类
    const serviceTypeRes = await getDictDataPage({
      dictType: 'service_type',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    serviceTypeOptions.value = (serviceTypeRes.data?.list || serviceTypeRes.list || []).map(
      (item: any) => ({
        label: item.label,
        value: item.value
      })
    )

    // 价格单位
    const priceUnitRes = await getDictDataPage({
      dictType: 'price_unit',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    priceUnitOptions.value = (priceUnitRes.data?.list || priceUnitRes.list || []).map(
      (item: any) => ({
        label: item.label,
        value: item.value
      })
    )

    // 套餐类型
    const packageTypeRes = await getDictDataPage({
      dictType: 'package_type',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    packageTypeOptions.value = (packageTypeRes.data?.list || packageTypeRes.list || []).map(
      (item: any) => ({
        label: item.label,
        value: item.value
      })
    )

    // 预约时间范围
    const appointmentTimeRangeRes = await getDictDataPage({
      dictType: 'appointment_time_range',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    appointmentTimeRangeOptions.value = (
      appointmentTimeRangeRes.data?.list ||
      appointmentTimeRangeRes.list ||
      []
    ).map((item: any) => ({
      label: item.label,
      value: item.value
    }))

    // 服务开始时间
    const serviceStartTimeRes = await getDictDataPage({
      dictType: 'service_start_time',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    serviceStartTimeOptions.value = (
      serviceStartTimeRes.data?.list ||
      serviceStartTimeRes.list ||
      []
    ).map((item: any) => ({
      label: item.label,
      value: item.value
    }))

    // 时间选择模式
    const timeSelectionModeRes = await getDictDataPage({
      dictType: 'time_selection_mode',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    timeSelectionModeOptions.value = (
      timeSelectionModeRes.data?.list ||
      timeSelectionModeRes.list ||
      []
    ).map((item: any) => ({
      label: item.label,
      value: item.value
    }))

    // 地址设置
    const addressSettingRes = await getDictDataPage({
      dictType: 'address_setting',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    addressSettingOptions.value = (
      addressSettingRes.data?.list ||
      addressSettingRes.list ||
      []
    ).map((item: any) => ({
      label: item.label,
      value: item.value
    }))

    // 预约模式
    const appointmentModeRes = await getDictDataPage({
      dictType: 'appointment_mode',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    appointmentModeOptions.value = (
      appointmentModeRes.data?.list ||
      appointmentModeRes.list ||
      []
    ).map((item: any) => ({
      label: item.label,
      value: item.value
    }))

    // 商品状态
    const productStatusRes = await getDictDataPage({
      dictType: 'product_status',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    productStatusOptions.value = (productStatusRes.data?.list || productStatusRes.list || []).map(
      (item: any) => ({
        label: item.label,
        value: item.value
      })
    )
  } catch (error) {
    console.error('加载数据字典失败:', error)
  }
}

// 添加特色标签
const addFeature = () => {
  if (newFeature.value.trim()) {
    const featureExists = formData.featureList.some(
      (f) => f.featureName === newFeature.value.trim()
    )
    if (!featureExists) {
      formData.featureList.push({
        featureName: newFeature.value.trim(),
        sortOrder: formData.featureList.length + 1
      })
    }
    newFeature.value = ''
  }
}

// 移除特色标签
const removeFeature = (index: number) => {
  formData.featureList.splice(index, 1)
}

// 添加推荐标签
const addRecommendedTag = (tag: string) => {
  const featureExists = formData.featureList.some((f) => f.featureName === tag)
  if (!featureExists) {
    formData.featureList.push({
      featureName: tag,
      sortOrder: formData.featureList.length + 1
    })
  }
}

// 图片上传前校验
const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 主图上传处理
const handleImageUpload = async (options: any) => {
  try {
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      formData.thumbnail = response.data
      ElMessage.success('主图上传成功')
    }
  } catch (error) {
    console.error('主图上传失败:', error)
    ElMessage.error('主图上传失败')
  }
}

// 轮播图上传处理
const handleCarouselImageUpload = async (options: any) => {
  try {
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      formData.carouselList.push({
        imageUrl: response.data,
        sortOrder: formData.carouselList.length + 1,
        status: 1
      })
      ElMessage.success('轮播图上传成功')
    }
  } catch (error) {
    console.error('轮播图上传失败:', error)
    ElMessage.error('轮播图上传失败')
  }
}

// 格式化文本
const formatText = (type: string) => {
  // 这里可以实现富文本编辑功能
  console.log('格式化文本:', type)
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    category: '',
    thumbnail: '',
    carouselList: [],
    price: 0,
    unit: '次',
    serviceDuration: '',
    packageType: 'count-card',
    taskSplitRule: '',
    featureList: [],
    serviceDescription: '',
    serviceDetails: '',
    serviceProcess: '',
    purchaseNotice: '',
    status: 'active',
    advanceBookingDays: 1,
    timeSelectionMode: 'fixed',
    appointmentMode: 'start-date',
    serviceStartTime: 'within-3-days',
    addressSetting: 'fixed',
    maxBookingDays: 30,
    cancellationPolicy: ''
  })
  newFeature.value = ''
}

// 获取套餐列表
const getPackageList = async () => {
  loading.value = true
  try {
    const params = {
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize,
      keyword: filterForm.keyword,
      category: filterForm.category,
      status: activeTab.value
    }

    const response = await getServicePackageList(params)
    packageList.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    console.error('获取套餐列表失败:', error)
    ElMessage.error('获取套餐列表失败')
  } finally {
    loading.value = false
  }
}

// 获取套餐类型标签
const getPackageTypeTag = (type: string) => {
  switch (type) {
    case 'count-card':
      return 'danger'
    case 'long-term':
      return 'primary'
    default:
      return 'info'
  }
}

// 获取套餐类型文本
const getPackageTypeText = (type: string) => {
  switch (type) {
    case 'count-card':
      return '次数次卡套餐'
    case 'long-term':
      return '长周期套餐'
    default:
      return '未知'
  }
}

// 获取状态标签
const getStatusTag = (status: string) => {
  switch (status) {
    case 'active':
      return 'success'
    case 'pending':
      return 'warning'
    case 'deleted':
      return 'info'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return '已上架'
    case 'pending':
      return '待上架'
    case 'deleted':
      return '回收站'
    default:
      return '未知'
  }
}

// 获取切换状态文本
const getToggleStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return '下架'
    case 'pending':
      return '上架'
    default:
      return '启用'
  }
}

// 查询
const handleSearch = () => {
  pagination.currentPage = 1
  getPackageList()
}

// 重置
const handleReset = () => {
  filterForm.keyword = ''
  filterForm.category = ''
  pagination.currentPage = 1
  getPackageList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  getPackageList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  getPackageList()
}

// 新增套餐
const handleAdd = () => {
  isEdit.value = false
  currentId.value = 0
  resetForm()
  drawerVisible.value = true
}

// 编辑套餐
const handleEdit = async (row: ServicePackage) => {
  try {
    isEdit.value = true
    currentId.value = row.id

    // 获取套餐详情
    const response = await getServicePackageDetail(row.id)
    const detail = response.data

    // 填充表单数据
    Object.assign(formData, {
      name: detail.name,
      category: detail.category,
      thumbnail: detail.thumbnail,
      carouselList: detail.carouselList || [],
      price: detail.price,
      originalPrice: detail.originalPrice,
      unit: detail.unit,
      serviceDuration: detail.serviceDuration,
      packageType: detail.packageType,
      taskSplitRule: detail.taskSplitRule,
      featureList: detail.featureList || [],
      serviceDescription: detail.serviceDescription,
      serviceDetails: detail.serviceDetails,
      serviceProcess: detail.serviceProcess,
      purchaseNotice: detail.purchaseNotice,
      status: detail.status,
      advanceBookingDays: detail.advanceBookingDays,
      timeSelectionMode: detail.timeSelectionMode,
      appointmentMode: detail.appointmentMode,
      serviceStartTime: detail.serviceStartTime,
      addressSetting: detail.addressSetting,
      maxBookingDays: detail.maxBookingDays,
      cancellationPolicy: detail.cancellationPolicy
    })

    drawerVisible.value = true
  } catch (error) {
    console.error('获取套餐详情失败:', error)
    ElMessage.error('获取套餐详情失败')
  }
}

// 切换状态
const handleToggleStatus = async (row: ServicePackage) => {
  try {
    let newStatus = row.status
    if (row.status === 'active') {
      newStatus = 'pending'
    } else if (row.status === 'pending') {
      newStatus = 'active'
    }

    await updateSingleServicePackageStatus(row.id, newStatus)
    ElMessage.success('状态更新成功')
    getPackageList()
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('更新状态失败')
  }
}

// 删除套餐
const handleDelete = async (row: ServicePackage) => {
  try {
    await ElMessageBox.confirm(`确定要删除套餐"${row.name}"吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteServicePackage(row.id)
    ElMessage.success('删除成功')
    getPackageList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (isEdit.value) {
      await updateServicePackage({
        id: currentId.value,
        ...formData
      })
      ElMessage.success('更新成功')
    } else {
      await createServicePackage(formData)
      ElMessage.success('创建成功')
    }

    drawerVisible.value = false
    getPackageList()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 关闭抽屉
const handleCloseDrawer = () => {
  drawerVisible.value = false
  resetForm()
}

// 监听标签页变化
const handleTabChange = () => {
  pagination.currentPage = 1
  getPackageList()
}

// 生命周期
onMounted(() => {
  loadDictOptions()
  getPackageList()
})
</script>

<style scoped lang="scss">
.service-package-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    font-size: 24px;
    color: #343a40;
    display: flex;
    align-items: center;

    i {
      margin-right: 10px;
      color: #3498db;
    }
  }
}

.status-tabs {
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  :deep(.el-tabs__header) {
    margin: 0;
    padding: 0 20px;
    border-bottom: 1px solid #dee2e6;
  }

  :deep(.el-tabs__content) {
    padding: 20px;
  }

  :deep(.el-tabs__item) {
    position: relative;
    display: flex;
    align-items: center;

    .tab-badge {
      margin-left: 6px;
      background: #e9ecef;
      color: #343a40;
      border-radius: 12px;
      padding: 2px 8px;
      font-size: 11px;
      font-weight: 500;
      line-height: 1;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      min-width: 16px;
      height: 16px;
    }
  }

  :deep(.el-tabs__item.is-active) {
    .tab-badge {
      background: #3498db;
      color: white;
    }
  }
}

.filter-section {
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.package-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  :deep(.el-table) {
    width: 100%;
  }

  :deep(.el-table th) {
    background-color: #f8f9fa;
    color: #333;
    font-weight: 600;
    font-size: 14px;
  }

  :deep(.el-table td) {
    color: #666;
    font-size: 14px;
  }

  :deep(.el-table .el-tag) {
    margin-right: 5px;
    margin-bottom: 5px;
  }

  :deep(.el-table .el-image) {
    border-radius: 4px;
  }

  :deep(.el-table .el-button--text) {
    padding: 0;
    margin-right: 10px;
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;

  .total-count {
    font-size: 14px;
    color: #666;
  }

  :deep(.el-pagination) {
    .el-pagination__total {
      display: none;
    }
  }
}

.editor-wrapper {
  border: 1px solid #dcdfe6;
  border-radius: 4px;

  .editor {
    min-height: 200px;
    padding: 10px;
  }
}

// 表单抽屉样式
.package-form-drawer {
  :deep(.el-drawer__wrapper) {
    z-index: 2000;
  }

  :deep(.el-drawer__container) {
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  }

  :deep(.el-drawer__header) {
    padding: 20px 24px;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 0;
    background: #fff;

    .el-drawer__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .el-drawer__close-btn {
      font-size: 18px;
      color: #909399;

      &:hover {
        color: #409eff;
      }
    }
  }

  :deep(.el-drawer__body) {
    padding: 0;
    height: calc(100vh - 120px);
    overflow-y: auto;
    background: #f5f7fa;
  }

  :deep(.el-drawer__footer) {
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
    background: #fff;
    position: sticky;
    bottom: 0;
    z-index: 10;
  }

  .form-section {
    margin-bottom: 30px;
    padding: 20px 24px;
    background: #fff;
    border-radius: 8px;
    margin: 20px 24px 30px 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: 600;
      color: #333;

      i {
        margin-right: 8px;
        color: #3498db;
      }
    }

    .form-tip {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
      line-height: 1.4;
    }
  }

  .image-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 160px;
    height: 160px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fafafa;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    .upload-image {
      width: 160px;
      height: 160px;
      object-fit: cover;
    }

    .upload-icon {
      font-size: 28px;
      color: #8c939d;
      margin-bottom: 8px;
    }

    .upload-text {
      font-size: 14px;
      color: #666;
      margin-bottom: 4px;
    }

    .upload-hint {
      font-size: 12px;
      color: #999;
    }
  }

  .carousel-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 120px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fafafa;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    .upload-icon {
      font-size: 24px;
      color: #8c939d;
      margin-bottom: 4px;
    }

    .upload-text {
      font-size: 12px;
      color: #666;
    }
  }

  .feature-input {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .el-button {
      margin-left: 10px;
    }
  }

  .feature-tags {
    margin-bottom: 10px;
  }

  .rich-editor {
    border: 1px solid #dcdfe6;
    border-radius: 4px;

    .editor-toolbar {
      padding: 8px;
      border-bottom: 1px solid #dcdfe6;
      background: #f5f7fa;

      .el-button {
        margin-right: 5px;
        padding: 4px 8px;
        font-size: 12px;
      }
    }
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>
