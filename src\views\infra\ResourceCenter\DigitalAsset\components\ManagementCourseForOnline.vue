<!--
  页面名称：线上课程管理
  功能描述：管理线上课程的详细信息，包括课程设置、课程大纲等
-->
<template>
  <el-card>
    <div style="margin-bottom: 16px">
      <el-button type="default" @click="goBack" icon="el-icon-arrow-left">返回列表</el-button>
    </div>
    <el-tabs v-model="activeTab">
      <!-- 课程设置 -->
      <el-tab-pane label="课程设置" name="setting">
        <el-form
          :model="form"
          :rules="rules"
          ref="formRef"
          label-width="120px"
          class="course-form"
          v-loading="loading"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="课程名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入课程名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="课程状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择">
                  <el-option
                    v-for="item in statusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="课程分类" prop="category">
                <el-select v-model="form.category" placeholder="请选择">
                  <el-option
                    v-for="item in categoryOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="课程封面" prop="cover">
                <el-upload
                  class="upload-demo"
                  action="#"
                  :auto-upload="false"
                  :show-file-list="true"
                  :limit="1"
                  :on-change="handleCoverChange"
                  :file-list="form.coverUrl ? [{ url: form.coverUrl, name: '课程封面' }] : []"
                  list-type="picture-card"
                >
                  <el-icon><Plus /></el-icon>
                  <template #tip>
                    <div style="color: #888; margin-left: 10px; display: inline-block"
                      >仅支持上传1张图片，上传后自动填充URL</div
                    >
                  </template>
                </el-upload>
                <div v-if="form.coverUrl" style="margin-top: 8px">
                  <el-image
                    :src="form.coverUrl"
                    style="width: 80px; height: 80px; object-fit: contain; border: 1px solid #eee"
                    :preview-src-list="[form.coverUrl]"
                  />
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="关联讲师" prop="teacherName">
                <el-select v-model="form.teacherName" placeholder="请选择讲师">
                  <el-option
                    v-for="item in teacherOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收款商户" prop="merchant">
                <el-select
                  v-model="form.merchant"
                  placeholder="请选择收款商户"
                  @change="handleMerchantChange"
                >
                  <el-option
                    v-for="item in merchantOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="业务模块" prop="businessModule">
                <el-select v-model="form.businessModule" placeholder="请选择业务模块">
                  <el-option
                    v-for="item in businessModuleOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="课程总时长(小时)" prop="totalDuration">
                <el-input
                  v-model="form.totalDuration"
                  placeholder="请输入课程总时长"
                  type="number"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="已报名人数" prop="enrolledCount">
                <el-input
                  v-model="form.enrolledCount"
                  placeholder="请输入已报名人数"
                  type="number"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="课程详情介绍" prop="description">
                <el-input
                  v-model="form.description"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入课程详细介绍"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <div style="text-align: right; margin-top: 24px">
            <el-button @click="onCancel" :disabled="loading">取消</el-button>
            <el-button type="primary" @click="onSubmit" :loading="loading">保存</el-button>
          </div>
        </el-form>
      </el-tab-pane>

      <!-- 课程大纲 -->
      <el-tab-pane label="课程大纲" name="outline">
        <div class="outline-section" v-loading="chaptersLoading">
          <div class="outline-header">
            <div class="outline-title">课程大纲</div>
            <el-button type="primary" @click="addChapter" :disabled="!form.id"
              >+ 添加章节</el-button
            >
          </div>

          <!-- 章节列表 -->
          <div class="chapter-list" v-if="form.chapters.length > 0">
            <div
              v-for="(chapter, chapterIndex) in form.chapters"
              :key="chapterIndex"
              class="chapter-item"
            >
              <div class="chapter-header">
                <div class="chapter-info">
                  <span class="chapter-title">第{{ chapterIndex + 1 }}章：{{ chapter.title }}</span>
                </div>
                <div class="chapter-actions">
                  <el-button size="small" @click="editChapter(chapterIndex)">编辑</el-button>
                  <el-button size="small" type="danger" @click="removeChapter(chapterIndex)"
                    >删除</el-button
                  >
                </div>
              </div>

              <!-- 课时列表 -->
              <div class="lesson-list" v-if="chapter.lessons.length > 0">
                <div
                  v-for="(lesson, lessonIndex) in chapter.lessons"
                  :key="lessonIndex"
                  class="lesson-item"
                >
                  <div class="lesson-info">
                    <span class="lesson-title">{{ lessonIndex + 1 }}. {{ lesson.title }}</span>
                    <el-tag
                      size="small"
                      :type="lesson.lessonType === '视频' ? 'primary' : 'success'"
                      >{{ lesson.lessonType }}</el-tag
                    >
                    <el-tag v-if="lesson.isFree" size="small" type="warning">免费试看</el-tag>
                    <span v-if="lesson.materialName" class="lesson-material">{{
                      lesson.materialName
                    }}</span>
                  </div>
                  <div class="lesson-actions">
                    <el-button size="small" @click="editLesson(chapterIndex, lessonIndex)"
                      >编辑</el-button
                    >
                    <el-button
                      size="small"
                      type="danger"
                      @click="removeLesson(chapterIndex, lessonIndex)"
                      >删除</el-button
                    >
                  </div>
                </div>
              </div>

              <div class="add-lesson">
                <el-button size="small" type="primary" plain @click="addLesson(chapterIndex)"
                  >+ 添加课时</el-button
                >
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <el-empty v-else description="请从左侧添加第一个课程章节" />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 章节编辑弹窗 -->
    <el-dialog v-model="chapterDialogVisible" title="编辑章节" width="500px">
      <el-form :model="currentChapter" label-width="80px">
        <el-form-item label="章节标题">
          <el-input v-model="currentChapter.title" placeholder="请输入章节标题" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="chapterDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveChapter">保存</el-button>
      </template>
    </el-dialog>

    <!-- 课时编辑弹窗 -->
    <el-dialog v-model="lessonDialogVisible" title="编辑课时" width="500px">
      <el-form :model="currentLesson" label-width="80px">
        <el-form-item label="课时标题">
          <el-input v-model="currentLesson.title" placeholder="请输入课时标题" />
        </el-form-item>
        <el-form-item label="课时类型">
          <el-select
            v-model="currentLesson.lessonType"
            placeholder="请选择课时类型"
            style="width: 100%"
          >
            <el-option label="视频" value="视频" />
            <el-option label="文档" value="文档" />
            <el-option label="音频" value="音频" />
          </el-select>
        </el-form-item>
        <el-form-item label="免费试看">
          <el-checkbox v-model="currentLesson.isFree">允许免费试看</el-checkbox>
        </el-form-item>
        <el-form-item label="关联素材">
          <el-select
            v-model="currentLesson.materialName"
            placeholder="请选择关联素材"
            style="width: 100%"
          >
            <el-option
              v-for="item in materialOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="lessonDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveLesson">保存</el-button>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import {
  DigitalAssetApi,
  type DigitalAssetCourse,
  type CourseChapter,
  type CourseLesson,
  type AddChapterParams,
  type UpdateChapterParams,
  type AddLessonParams,
  type UpdateLessonParams
} from '@/api/infra/digitalAsset'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { getAppList } from '@/api/pay/app'
import request from '@/config/axios'

/** 组件属性定义 */
interface Props {
  course?: any
}

const props = defineProps<Props>()
const emit = defineEmits(['goBack'])

/** 响应式数据 */
const activeTab = ref('setting')
const formRef = ref()
const loading = ref(false)
const chaptersLoading = ref(false)
const chapterDialogVisible = ref(false)
const lessonDialogVisible = ref(false)
const currentChapterIndex = ref(-1)
const currentLessonIndex = ref(-1)

/** 表单数据 */
const form = reactive({
  id: undefined as number | undefined,
  name: '',
  teachType: '线上授课',
  coverUrl: '',
  category: '',
  status: '',
  teacherId: undefined as number | undefined,
  teacherName: '',
  businessModule: '',
  merchant: undefined as number | undefined,
  merchantName: '',
  description: '',
  // 线上授课专用字段
  totalDuration: undefined as number | undefined,
  enrolledCount: 0,
  // 章节数据（前端展示用）
  chapters: [] as Array<{
    id?: number
    title: string
    sortOrder?: number
    lessons: Array<{
      id?: number
      title: string
      lessonType: string
      isFree: boolean
      materialName: string
      materialFileUrl?: string
    }>
  }>
})

/** 当前编辑的章节和课时 */
const currentChapter = reactive({
  id: undefined as number | undefined,
  courseId: 0,
  title: '',
  sortOrder: 0
})

const currentLesson = reactive({
  id: undefined as number | undefined,
  courseId: 0,
  chapterId: 0,
  title: '',
  lessonType: '视频',
  isFree: false,
  materialId: '',
  materialName: '',
  materialFileUrl: '',
  sortOrder: 0
})

/** 表单校验规则 */
const rules = {
  name: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
  status: [{ required: true, message: '请选择课程状态', trigger: 'change' }],
  category: [{ required: true, message: '请选择课程分类', trigger: 'change' }]
}

/** 下拉选项数据 */
const statusOptions = [
  { label: '已上架', value: '已上架' },
  { label: '已下架', value: '已下架' },
  { label: '待发布', value: '待发布' }
]

const categoryOptions = [
  { label: '家政技能', value: '家政技能' },
  { label: '职业素养', value: '职业素养' },
  { label: '高校实践', value: '高校实践' },
  { label: '企业管理', value: '企业管理' }
]

const merchantOptions = ref<any[]>([])

const businessModuleOptions = ref<any[]>([])

const materialOptions = [
  {
    label: '课程导论.mp4 (课程的整体介绍和学习方法)',
    value: '课程导论.mp4 (课程的整体介绍和学习方法)'
  },
  { label: '开课须知.md (课程学习注意事项)', value: '开课须知.md (课程学习注意事项)' },
  { label: 'SWOT分析模板.xlsx (实用工具模板)', value: 'SWOT分析模板.xlsx (实用工具模板)' },
  { label: '案例分析.pdf (经典案例解析)', value: '案例分析.pdf (经典案例解析)' }
]

const teacherOptions = [
  { label: '王老师', value: '王老师' },
  { label: '李教授', value: '李教授' },
  { label: '张讲师', value: '张讲师' },
  { label: '刘老师', value: '刘老师' },
  { label: '陈教授', value: '陈教授' }
]

/** API调用方法 */
// 获取课程详情
const fetchCourseDetail = async (id: number) => {
  try {
    loading.value = true
    const result = await DigitalAssetApi.getCourseDetail(id)

    // 填充表单数据
    Object.assign(form, {
      id: result.id,
      name: result.name,
      teachType: result.teachType,
      coverUrl: result.coverUrl || '',
      category: result.category,
      status: result.status,
      teacherId: result.teacherId,
      teacherName: result.teacherName || '',
      businessModule: result.businessModule || '',
      merchant: result.merchant,
      merchantName: result.merchantName || '',
      description: result.description || '',
      totalDuration: result.totalDuration,
      enrolledCount: result.enrolledCount || 0
    })
  } catch (error) {
    console.error('获取课程详情失败:', error)
    ElMessage.error('获取课程详情失败，请重试')
  } finally {
    loading.value = false
  }
}

// 更新课程信息
const updateCourse = async () => {
  try {
    loading.value = true

    const updateData: DigitalAssetCourse = {
      id: form.id!,
      name: form.name,
      teachType: form.teachType,
      coverUrl: form.coverUrl || undefined,
      category: form.category,
      status: form.status,
      teacherId: form.teacherId || undefined,
      teacherName: form.teacherName || undefined,
      businessModule: form.businessModule || undefined,
      merchant: form.merchant || undefined,
      merchantName: form.merchantName || undefined,
      description: form.description || undefined,
      totalDuration: form.totalDuration || undefined,
      enrolledCount: form.enrolledCount || 0
    }

    await DigitalAssetApi.updateCourse(updateData)
    ElMessage.success('课程更新成功')
  } catch (error) {
    console.error('更新课程失败:', error)
    ElMessage.error('更新课程失败，请重试')
  } finally {
    loading.value = false
  }
}

/** 方法定义 */
function onCancel() {
  emit('goBack')
}

async function onSubmit() {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    await updateCourse()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

function goBack() {
  emit('goBack')
}

/** 封面上传相关方法 */
function handleCoverChange(file: any) {
  if (file && file.raw) {
    const formData = new FormData()
    formData.append('file', file.raw)
    formData.append('directory', 'course')
    request
      .postOriginal({
        url: '/infra/file/upload',
        data: formData,
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      .then((res) => {
        if (res.code === 0 && res.data && /^https?:\/\//.test(res.data)) {
          form.coverUrl = res.data
          ElMessage.success('上传成功')
        } else {
          form.coverUrl = ''
          ElMessage.error(res.msg || '上传失败')
        }
      })
      .catch(() => {
        form.coverUrl = ''
        ElMessage.error('上传失败')
      })
  }
}

function handleMerchantChange(merchantId: number) {
  // 根据选择的商户ID设置商户信息
  const merchant = merchantOptions.value.find((item) => item.id === merchantId)
  if (merchant) {
    form.merchant = merchant.id
    form.merchantName = merchant.name
  }
}

/** 章节管理方法 */
function addChapter() {
  // 重置表单
  Object.assign(currentChapter, {
    id: undefined,
    courseId: form.id || 0,
    title: '',
    sortOrder: form.chapters.length + 1
  })
  currentChapterIndex.value = -1
  chapterDialogVisible.value = true
}

function editChapter(index: number) {
  const chapter = form.chapters[index]
  Object.assign(currentChapter, {
    id: chapter.id,
    courseId: form.id || 0,
    title: chapter.title,
    sortOrder: chapter.sortOrder || index + 1
  })
  currentChapterIndex.value = index
  chapterDialogVisible.value = true
}

async function saveChapter() {
  console.log('saveChapter 被调用')
  console.log('currentChapter:', currentChapter)
  console.log('currentChapterIndex:', currentChapterIndex.value)
  console.log('form.id:', form.id)

  if (!currentChapter.title) {
    ElMessage.warning('请输入章节标题')
    return
  }

  try {
    if (currentChapterIndex.value === -1) {
      // 新增章节
      const params: AddChapterParams = {
        courseId: currentChapter.courseId,
        title: currentChapter.title,
        sortOrder: currentChapter.sortOrder
      }
      console.log('调用新增章节接口，参数:', params)
      const result = await DigitalAssetApi.addChapter(params)
      console.log('新增章节接口返回:', result)
      ElMessage.success('章节添加成功')

      // 刷新章节列表
      if (form.id) {
        await fetchChapterList(form.id)
      }
    } else {
      // 编辑章节
      const params: UpdateChapterParams = {
        id: currentChapter.id!,
        courseId: currentChapter.courseId,
        title: currentChapter.title,
        sortOrder: currentChapter.sortOrder
      }
      console.log('调用更新章节接口，参数:', params)
      await DigitalAssetApi.updateChapter(params)
      console.log('更新章节接口调用成功')
      ElMessage.success('章节更新成功')

      // 刷新章节列表
      if (form.id) {
        await fetchChapterList(form.id)
      }
    }

    chapterDialogVisible.value = false
  } catch (error) {
    console.error('保存章节失败:', error)
    ElMessage.error('保存章节失败，请重试')
  }
}

async function removeChapter(index: number) {
  const chapter = form.chapters[index]
  if (!chapter.id) {
    // 如果是本地新增的章节，直接删除
    form.chapters.splice(index, 1)
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除章节"${chapter.title}"吗？删除后该章节下的所有课时也将被删除。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await DigitalAssetApi.deleteChapter(chapter.id)
    ElMessage.success('章节删除成功')

    // 刷新章节列表
    if (form.id) {
      await fetchChapterList(form.id)
    }
  } catch (error) {
    if (error === 'cancel') {
      return
    }
    console.error('删除章节失败:', error)
    ElMessage.error('删除章节失败，请重试')
  }
}

/** 课时管理方法 */
function addLesson(chapterIndex: number) {
  const chapter = form.chapters[chapterIndex]
  // 重置表单
  Object.assign(currentLesson, {
    id: undefined,
    courseId: form.id || 0,
    chapterId: chapter.id || 0,
    title: '',
    lessonType: '视频',
    isFree: false,
    materialId: '',
    materialName: '',
    materialFileUrl: '',
    sortOrder: chapter.lessons.length + 1
  })
  currentChapterIndex.value = chapterIndex
  currentLessonIndex.value = -1
  lessonDialogVisible.value = true
}

function editLesson(chapterIndex: number, lessonIndex: number) {
  const lesson = form.chapters[chapterIndex].lessons[lessonIndex]
  const chapter = form.chapters[chapterIndex]
  Object.assign(currentLesson, {
    id: lesson.id,
    courseId: form.id || 0,
    chapterId: chapter.id || 0,
    title: lesson.title,
    lessonType: lesson.lessonType,
    isFree: lesson.isFree || false,
    materialId: '',
    materialName: lesson.materialName || '',
    materialFileUrl: lesson.materialFileUrl || '',
    sortOrder: lessonIndex + 1
  })
  currentChapterIndex.value = chapterIndex
  currentLessonIndex.value = lessonIndex
  lessonDialogVisible.value = true
}

async function saveLesson() {
  console.log('saveLesson 被调用')
  console.log('currentLesson:', currentLesson)
  console.log('currentLessonIndex:', currentLessonIndex.value)
  console.log('currentChapterIndex:', currentChapterIndex.value)
  console.log('form.id:', form.id)

  if (!currentLesson.title) {
    ElMessage.warning('请输入课时标题')
    return
  }

  try {
    if (currentLessonIndex.value === -1) {
      // 新增课时
      const params: AddLessonParams = {
        courseId: currentLesson.courseId,
        chapterId: currentLesson.chapterId,
        title: currentLesson.title,
        lessonType: currentLesson.lessonType,
        isFree: currentLesson.isFree,
        materialId: currentLesson.materialId || undefined,
        materialName: currentLesson.materialName || undefined,
        materialFileUrl: currentLesson.materialFileUrl || undefined,
        sortOrder: currentLesson.sortOrder
      }
      console.log('调用新增课时接口，参数:', params)
      await DigitalAssetApi.addLesson(params)
      console.log('新增课时接口调用成功')
      ElMessage.success('课时添加成功')

      // 刷新章节列表
      if (form.id) {
        await fetchChapterList(form.id)
      }
    } else {
      // 编辑课时
      const params: UpdateLessonParams = {
        id: currentLesson.id!,
        courseId: currentLesson.courseId,
        chapterId: currentLesson.chapterId,
        title: currentLesson.title,
        lessonType: currentLesson.lessonType,
        isFree: currentLesson.isFree,
        materialId: currentLesson.materialId || undefined,
        materialName: currentLesson.materialName || undefined,
        materialFileUrl: currentLesson.materialFileUrl || undefined,
        sortOrder: currentLesson.sortOrder
      }
      await DigitalAssetApi.updateLesson(params)
      ElMessage.success('课时更新成功')

      // 刷新章节列表
      if (form.id) {
        await fetchChapterList(form.id)
      }
    }

    lessonDialogVisible.value = false
  } catch (error) {
    console.error('保存课时失败:', error)
    ElMessage.error('保存课时失败，请重试')
  }
}

async function removeLesson(chapterIndex: number, lessonIndex: number) {
  const lesson = form.chapters[chapterIndex].lessons[lessonIndex]

  // 验证课时ID是否存在
  if (!lesson.id) {
    ElMessage.error('课时ID不存在，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除课时"${lesson.title}"吗？删除后无法恢复。`, '确认删除', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    })

    console.log('调用删除课时接口，课时ID:', lesson.id)
    await DigitalAssetApi.deleteLesson(lesson.id)
    console.log('删除课时接口调用成功')
    ElMessage.success('课时删除成功')

    // 刷新章节列表
    if (form.id) {
      await fetchChapterList(form.id)
    }
  } catch (error) {
    if (error === 'cancel') {
      return
    }
    console.error('删除课时失败:', error)
    ElMessage.error('删除课时失败，请重试')
  }
}

// 获取课程章节列表
const fetchChapterList = async (courseId: number) => {
  try {
    chaptersLoading.value = true
    console.log('调用获取章节列表接口，courseId:', courseId)
    const result: any = await DigitalAssetApi.getChapterList(courseId)
    console.log('获取章节列表接口返回:', result)

    // 根据实际返回的数据格式处理
    // 后端返回格式：{code: 0, data: [...], msg: ""}
    let chapterList: any[] = []

    if (result && Array.isArray(result)) {
      // 如果 request 封装已经提取了 data 部分，直接返回数组
      chapterList = result as any[]
    } else if (result && result.data && Array.isArray(result.data)) {
      // 完整响应格式：{code: 0, data: [...], msg: ""}
      chapterList = result.data
    } else if (result && result.list && Array.isArray(result.list)) {
      // 备用格式：{list: [...]}
      chapterList = result.list
    } else {
      console.warn('章节列表数据格式不正确:', result)
      chapterList = []
    }

    console.log('处理后的章节列表:', chapterList)

    // 更新表单中的章节数据
    form.chapters = chapterList.map((chapter: any) => ({
      id: chapter.id,
      title: chapter.title || '',
      sortOrder: chapter.sortOrder || 0,
      lessons:
        chapter.lessons && Array.isArray(chapter.lessons)
          ? chapter.lessons.map((lesson: any) => ({
              id: lesson.id,
              title: lesson.title || '',
              lessonType: lesson.lessonType || '视频',
              isFree: lesson.isFree || false,
              materialName: lesson.materialName || '',
              materialFileUrl: lesson.materialFileUrl || ''
            }))
          : []
    }))

    console.log('更新后的表单章节数据:', form.chapters)
  } catch (error) {
    console.error('获取章节列表失败:', error)
    ElMessage.error('获取章节列表失败，请重试')
    // 确保在出错时不会破坏现有数据
    if (!form.chapters) {
      form.chapters = []
    }
  } finally {
    chaptersLoading.value = false
  }
}

/** 获取业务模块字典数据 */
const fetchBusinessModuleOptions = async () => {
  try {
    const res = await getDictDataPage({ dictType: 'business_module', pageNo: 1, pageSize: 100 })
    businessModuleOptions.value = (res?.list || []).map((item) => ({
      label: item.label,
      value: item.value
    }))
  } catch (error) {
    console.error('获取业务模块字典数据失败:', error)
  }
}

/** 获取商户列表数据 */
const fetchMerchantOptions = async () => {
  try {
    const res = await getAppList()
    console.log('支付应用接口返回数据:', res)

    if (Array.isArray(res)) {
      console.log('数据直接在res中，数组长度:', res.length)
      // 直接使用应用数据作为商户选项
      merchantOptions.value = res.map((app: any) => ({
        id: app.id,
        name: app.name,
        label: app.name,
        value: app.id
      }))
      console.log('处理后的商户选项:', merchantOptions.value)
    } else {
      console.log('支付应用接口返回数据格式不符合预期:', res)
    }
  } catch (error) {
    console.error('获取商户列表失败:', error)
  }
}

// 页面加载时获取课程详情
onMounted(() => {
  fetchBusinessModuleOptions()
  fetchMerchantOptions()
  if (props.course?.id) {
    fetchCourseDetail(props.course.id)
    fetchChapterList(props.course.id)
  }
})

// 监听 props 变化
watch(
  () => props.course,
  (newCourse) => {
    if (newCourse?.id) {
      fetchCourseDetail(newCourse.id)
      fetchChapterList(newCourse.id)
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.course-form {
  max-width: 900px;
}

.outline-section {
  max-width: 900px;
  margin: 0 auto;
}

.outline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.outline-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.chapter-list {
  .chapter-item {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;

    .chapter-header {
      background: #f5f7fa;
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e4e7ed;

      .chapter-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .chapter-title {
          font-weight: 500;
          color: #333;
        }

        .chapter-duration {
          color: #666;
          font-size: 12px;
        }
      }

      .chapter-actions {
        display: flex;
        gap: 8px;
      }
    }

    .lesson-list {
      .lesson-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &:last-child {
          border-bottom: none;
        }

        .lesson-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .lesson-title {
            color: #333;
          }

          .lesson-duration {
            color: #666;
            font-size: 12px;
          }
        }

        .lesson-actions {
          display: flex;
          gap: 8px;
        }
      }
    }

    .add-lesson {
      padding: 12px 16px;
      text-align: center;
      background: #fafbfc;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chapter-header {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 8px;
  }

  .lesson-item {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 8px;
  }

  .lesson-info {
    flex-wrap: wrap;
  }
}
</style>
